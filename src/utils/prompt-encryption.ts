// 简单的prompt加密/解密工具
// 使用基于时间戳的动态密钥来混淆prompt内容

export class PromptEncryption {
  private static readonly ENCRYPTION_KEY = 'fillify_prompt_protection_2024'
  
  // 简单的XOR加密
  private static xorEncrypt(text: string, key: string): string {
    let result = ''
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      result += String.fromCharCode(charCode)
    }
    return result
  }

  // Base64编码以避免特殊字符问题
  private static base64Encode(str: string): string {
    return btoa(unescape(encodeURIComponent(str)))
  }

  private static base64Decode(str: string): string {
    return decodeURIComponent(escape(atob(str)))
  }

  // 加密prompt
  static encryptPrompt(prompt: string): string {
    try {
      // 添加时间戳作为盐值
      const timestamp = Date.now().toString()
      const saltedPrompt = `${timestamp}:${prompt}`
      
      // XOR加密
      const encrypted = this.xorEncrypt(saltedPrompt, this.ENCRYPTION_KEY)
      
      // Base64编码
      return this.base64Encode(encrypted)
    } catch (error) {
      console.error('Prompt encryption failed:', error)
      return prompt // 失败时返回原始prompt
    }
  }

  // 解密prompt
  static decryptPrompt(encryptedPrompt: string): string {
    try {
      // Base64解码
      const decoded = this.base64Decode(encryptedPrompt)
      
      // XOR解密
      const decrypted = this.xorEncrypt(decoded, this.ENCRYPTION_KEY)
      
      // 移除时间戳盐值
      const colonIndex = decrypted.indexOf(':')
      if (colonIndex > 0) {
        return decrypted.substring(colonIndex + 1)
      }
      
      return decrypted
    } catch (error) {
      console.error('Prompt decryption failed:', error)
      return encryptedPrompt // 失败时返回原始内容
    }
  }

  // 生成混淆的请求体
  static createObfuscatedRequest(originalBody: any): any {
    const obfuscatedBody = { ...originalBody }
    
    // 混淆messages中的content
    if (obfuscatedBody.messages && Array.isArray(obfuscatedBody.messages)) {
      obfuscatedBody.messages = obfuscatedBody.messages.map((message: any) => {
        if (message.content && typeof message.content === 'string') {
          return {
            ...message,
            content: this.encryptPrompt(message.content)
          }
        }
        return message
      })
    }

    // 对于Gemini格式的请求
    if (obfuscatedBody.contents && Array.isArray(obfuscatedBody.contents)) {
      obfuscatedBody.contents = obfuscatedBody.contents.map((content: any) => {
        if (content.parts && Array.isArray(content.parts)) {
          return {
            ...content,
            parts: content.parts.map((part: any) => {
              if (part.text && typeof part.text === 'string') {
                return {
                  ...part,
                  text: this.encryptPrompt(part.text)
                }
              }
              return part
            })
          }
        }
        return content
      })
    }

    return obfuscatedBody
  }

  // 从混淆的请求体中恢复原始内容
  static restoreOriginalRequest(obfuscatedBody: any): any {
    const originalBody = { ...obfuscatedBody }
    
    // 恢复messages中的content
    if (originalBody.messages && Array.isArray(originalBody.messages)) {
      originalBody.messages = originalBody.messages.map((message: any) => {
        if (message.content && typeof message.content === 'string') {
          return {
            ...message,
            content: this.decryptPrompt(message.content)
          }
        }
        return message
      })
    }

    // 对于Gemini格式的请求
    if (originalBody.contents && Array.isArray(originalBody.contents)) {
      originalBody.contents = originalBody.contents.map((content: any) => {
        if (content.parts && Array.isArray(content.parts)) {
          return {
            ...content,
            parts: content.parts.map((part: any) => {
              if (part.text && typeof part.text === 'string') {
                return {
                  ...part,
                  text: this.decryptPrompt(part.text)
                }
              }
              return part
            })
          }
        }
        return content
      })
    }

    return originalBody
  }
}
