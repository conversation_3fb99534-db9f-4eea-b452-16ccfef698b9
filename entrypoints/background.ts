// 类型定义
import { CookieChangeInfo } from '../src/types'
import { createAIProvider, AIProviderError } from '../src/services/ai-providers'
import { generatePrompt, cleanAIResponse, FormRequest } from '../src/services/prompt-generator'

interface Logger {
  info: (...args: any[]) => void
  error: (...args: any[]) => void
  success: (...args: any[]) => void
}

interface UserInfo {
  id: string
  [key: string]: any
}

interface Settings {
  useCustomApi: boolean
  defaultProvider: string
  defaultModel: string
  ollama_endpoint?: string
}

interface ApiKeys {
  [provider: string]: string
}



interface Message {
  type: string
  [key: string]: any
}

interface DefaultModels {
  openai: string
  claude: string
  moonshot: string
  gemini: string
  openrouter: string
  ollama: string
}

// Background script
export default defineBackground(() => {
  // Helper function for logging - disabled for production
  const Logger: Logger = {
    info: (..._args) => {/* Production mode - logging disabled */},
    error: (...args) => console.error('[Formify Error]', ...args), // Keep error logging for critical issues
    success: (..._args) => {/* Production mode - logging disabled */}
  }

  // Constants
  const STORAGE_KEYS = {
    SETTINGS: 'formify_settings',
    API_KEYS: 'formify_api_keys',
    VALIDATED_KEYS: 'formify_validated_keys',
    SKIP_LOGIN: 'formify_skip_login',
    TOKEN_STATS: 'formify_token_stats',
    PROJECTS: 'formify_projects'
  } as const

  // API Base URL and Cookie Domain
  const API_BASE_URL = 'https://fillify-343190162770.asia-east1.run.app/api'
  const COOKIE_DOMAIN = 'fillify.tech'
  const COOKIE_URL = 'https://fillify.tech'

  // 全局登录状态管理
  let isLoggedIn = false
  let skipLogin = false

  // 初始化登录状态 - 立即执行
  async function initializeLoginStatus() {
    try {
      // 检查是否已选择跳过登录
      const { formify_skip_login } = await chrome.storage.sync.get(STORAGE_KEYS.SKIP_LOGIN)
      skipLogin = !!formify_skip_login
      
      // 立即检查登录状态
      await checkLoginStatus()
      
      Logger.info('Login status initialized:', { isLoggedIn, skipLogin })
    } catch (error) {
      Logger.error('Error initializing login status:', error)
    }
  }

  // 立即执行初始化
  initializeLoginStatus()

  // 用于追踪最后一次用户信息更新时间
  let lastUserInfoFetch = 0
  const USER_INFO_MAX_AGE = 5 * 60 * 1000 // 用户信息最大缓存时间（5分钟）

  // Provider Models Configuration
  const PROVIDER_MODELS = {
    openai: [
      { id: 'gpt-4', name: 'GPT-4' },
      { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
    ],
    claude: [
      { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
      { id: 'claude-2.1', name: 'Claude-2.1' }
    ],
    moonshot: [
      { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
      { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
      { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
    ],
    gemini: [
      { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash' },
      { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
    ],
    openrouter: [
      { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
      { id: 'openai/gpt-4', name: 'GPT-4' },
      { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo' },
      { id: 'anthropic/claude-3-opus', name: 'Claude-3 Opus' },
      { id: 'anthropic/claude-3-sonnet', name: 'Claude-3 Sonnet' },
      { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama-3 70B' },
      { id: 'mistralai/mistral-large', name: 'Mistral Large' }
    ],
    ollama: [
      { id: 'llama2', name: 'Llama 2' },
      { id: 'llama2:13b', name: 'Llama 2 13B' },
      { id: 'llama2:70b', name: 'Llama 2 70B' },
      { id: 'mistral', name: 'Mistral' },
      { id: 'codellama', name: 'Code Llama' },
      { id: 'phi', name: 'Phi' }
    ]
  } as const

  const defaultModels: DefaultModels = {
    openai: 'gpt-3.5-turbo',
    claude: 'claude-3-sonnet-20240229',
    moonshot: 'moonshot-v1-32k',
    gemini: 'gemini-2.0-flash',
    openrouter: 'openai/gpt-3.5-turbo',
    ollama: 'llama2'
  } as const;

  // 获取用户信息
  async function fetchUserInfo(userId: string, forceUpdate = false): Promise<UserInfo | null> {
    const now = Date.now()
    // 如果不是强制更新，且缓存未过期，直接返回
    if (!forceUpdate && now - lastUserInfoFetch < USER_INFO_MAX_AGE) {
      const { user_info } = await chrome.storage.local.get('user_info')
      if (user_info) {
        return user_info
      }
    }

    lastUserInfoFetch = now
    try {
      const response = await fetch(`${API_BASE_URL}/users/get-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      const data = await response.json()
      if (data.success && data.user) {
        await chrome.storage.local.set({
          user_info: data.user,
          user_info_timestamp: now
        })
        return data.user
      }
      return null
    } catch (error) {
      Logger.error('Error fetching user info:', error instanceof Error ? error.message : String(error))
      return null
    }
  }

  // 设置跳过登录状态
  async function setSkipLogin(skip: boolean): Promise<void> {
    try {
      skipLogin = skip
      await chrome.storage.sync.set({ [STORAGE_KEYS.SKIP_LOGIN]: skip })
      Logger.info('Skip login status updated:', skip)
    } catch (error) {
      Logger.error('Error setting skip login status:', error)
    }
  }



  // 检查登录状态 - 优化版本
  async function checkLoginStatus(): Promise<boolean> {
    try {
      if (!chrome.cookies) {
        Logger.error('Cookies API not available')
        return false
      }

      // 先检查是否跳过登录
      const { formify_skip_login } = await chrome.storage.sync.get(STORAGE_KEYS.SKIP_LOGIN)
      skipLogin = !!formify_skip_login

      const cookie = await chrome.cookies.get({
        url: COOKIE_URL,
        name: 'xToken'
      }).catch(error => {
        Logger.error('Error getting cookie:', error)
        return null
      })

      const newLoginState = !!cookie?.value

      if (newLoginState !== isLoggedIn) {
        isLoggedIn = newLoginState
        
        // 安全地发送登录状态变化消息，避免 "Receiving end does not exist" 错误
        try {
          // 使用 chrome.runtime.sendMessage 的安全包装
          const tabs = await chrome.tabs.query({})
          const activeTab = tabs.find(tab => tab.active) || tabs[0]
          
          if (activeTab && activeTab.id) {
            // 尝试发送到 content script
            try {
              await chrome.tabs.sendMessage(activeTab.id, {
                type: 'loginStatusChanged',
                isLoggedIn,
                skipLogin,
                token: cookie?.value
              })
            } catch (contentError) {
              // content script 可能还没加载，这是正常的
              console.debug('[Background] Content script not ready:', contentError.message)
            }
          }

          // 发送到 popup（如果打开的话）
          try {
            await chrome.runtime.sendMessage({
              type: 'loginStatusChanged',
              isLoggedIn,
              skipLogin,
              token: cookie?.value
            })
          } catch (popupError) {
            // popup 可能没有打开，这是正常的
            console.debug('[Background] Popup not open:', popupError.message)
          }
        } catch (error) {
          // 只有在确实是严重错误时才记录
          if (!error.message.includes('Could not establish connection') && 
              !error.message.includes('Receiving end does not exist')) {
            Logger.error('Error sending login status message:', error)
          }
        }

        try {
          if (!isLoggedIn) {
            await chrome.storage.local.remove('user_info')
          } else if (cookie?.value) {
            await fetchUserInfo(cookie.value, true)
          }
        } catch (error) {
          Logger.error('Error updating user info:', error)
        }
      }

      return isLoggedIn
    } catch (error) {
      Logger.error('Error checking login status:', error)
      return false
    }
  }

  async function checkAndUpdateLoginStatus(): Promise<boolean> {
    try {
      const loginStatus = await checkLoginStatus()
      if (loginStatus) {
        const cookie = await chrome.cookies.get({
          url: COOKIE_URL,
          name: 'xToken'
        }).catch(() => null)

        if (cookie?.value) {
          await fetchUserInfo(cookie.value).catch(error => {
            Logger.error('Error fetching user info:', error)
          })
        }
      }
      return loginStatus
    } catch (error) {
      Logger.error('Error in checkAndUpdateLoginStatus:', error)
      return false
    }
  }

  // 存储待处理的请求
  const pendingRequests = new Map<string, any>()

  // 隐藏AI请求的实现
  async function makeHiddenAIRequest(requestId: string): Promise<any> {
    const requestData = pendingRequests.get(requestId)
    if (!requestData) {
      throw new Error('Request data not found')
    }

    const { prompt, provider, model, apiKey, endpoint } = requestData

    // 根据不同提供商构建请求
    let url: string
    let headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }
    let body: any

    switch (provider) {
      case 'ollama':
        url = `${endpoint}/v1/chat/completions`
        headers['Authorization'] = 'Bearer ollama'
        headers['Accept'] = 'application/json'
        body = {
          model: model || 'llama2',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. IMPORTANT: You must ALWAYS return ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or additional content. Return ONLY the JSON object.'
            },
            {
              role: 'user',
              content: prompt + '\n\nIMPORTANT: Return ONLY a valid JSON object with no additional text or formatting.'
            }
          ],
          stream: false
        }
        break

      case 'openai':
        url = 'https://api.openai.com/v1/chat/completions'
        headers['Authorization'] = `Bearer ${apiKey}`
        body = {
          model: model || 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7
        }
        break

      case 'claude':
        url = 'https://api.anthropic.com/v1/messages'
        headers['x-api-key'] = apiKey
        headers['anthropic-version'] = '2023-06-01'
        body = {
          model: model || 'claude-3-sonnet-20240229',
          max_tokens: 1000,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        }
        break

      case 'moonshot':
        url = 'https://api.moonshot.cn/v1/chat/completions'
        headers['Authorization'] = `Bearer ${apiKey}`
        body = {
          model: model || 'moonshot-v1-8k',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7
        }
        break

      case 'gemini':
        url = `https://generativelanguage.googleapis.com/v1/models/${model || 'gemini-2.0-flash'}:generateContent?key=${apiKey}`
        body = {
          contents: [
            {
              role: "user",
              parts: [
                {
                  text: `You are a helpful assistant that generates form content based on descriptions. Always return valid JSON object without any markdown formatting or additional text. Here's the task:\n\n${prompt}`
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40
          }
        }
        break

      case 'deepseek':
        url = 'https://api.deepseek.com/v1/chat/completions'
        headers['Authorization'] = `Bearer ${apiKey}`
        body = {
          model: model || 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7
        }
        break

      case 'openrouter':
        url = 'https://openrouter.ai/api/v1/chat/completions'
        headers['Authorization'] = `Bearer ${apiKey}`
        headers['HTTP-Referer'] = 'https://fillify.tech'
        headers['X-Title'] = 'Fillify'
        body = {
          model: model || 'openai/gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON without any markdown formatting or additional text.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
          response_format: { type: 'json_object' }
        }
        break

      default:
        throw new Error(`Unsupported provider: ${provider}`)
    }

    // 使用XMLHttpRequest来隐藏请求（不会在开发者工具的Network面板中显示）
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      xhr.open('POST', url, true)

      // 设置请求头
      Object.entries(headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value)
      })

      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText)

              // 根据不同提供商处理响应
              let content: string
              let usage: any = { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }

              switch (provider) {
                case 'ollama':
                case 'openai':
                case 'moonshot':
                case 'deepseek':
                case 'openrouter':
                  content = response.choices[0].message.content
                  usage = response.usage || usage
                  break
                case 'claude':
                  content = response.content[0].text
                  usage = {
                    prompt_tokens: response.usage?.input_tokens || 0,
                    completion_tokens: response.usage?.output_tokens || 0,
                    total_tokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0)
                  }
                  break
                case 'gemini':
                  content = response.candidates[0].content.parts[0].text
                  usage = {
                    prompt_tokens: response.usageMetadata?.promptTokenCount || 0,
                    completion_tokens: response.usageMetadata?.candidatesTokenCount || 0,
                    total_tokens: response.usageMetadata?.totalTokenCount || 0
                  }
                  break
                default:
                  throw new Error(`Unsupported provider: ${provider}`)
              }

              resolve({ content, usage })
            } catch (error) {
              reject(new Error(`Failed to parse response: ${error}`))
            }
          } else {
            reject(new Error(`HTTP error: ${xhr.status} ${xhr.statusText}`))
          }
        }
      }

      xhr.onerror = function() {
        reject(new Error('Network error'))
      }

      xhr.send(JSON.stringify(body))
    })
  }

  // Handle AI request with request interception
  async function handleAiRequest(request: any) {
    try {
      Logger.info('Handling AI request:', request)

      const storage = await chrome.storage.sync.get([
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.API_KEYS,
        STORAGE_KEYS.VALIDATED_KEYS,
        STORAGE_KEYS.PROJECTS
      ])

      const settings: Settings = storage[STORAGE_KEYS.SETTINGS] || {}
      const apiKeys: ApiKeys = storage[STORAGE_KEYS.API_KEYS] || {}
      const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
      const projects = storage[STORAGE_KEYS.PROJECTS] || []

      const provider = settings.defaultProvider || 'openai'

      // 检查 API Key 是否已验证
      if (!validatedKeys[provider]) {
        throw new Error(`No validated API key for ${provider}`)
      }

      const model = settings.defaultModel || defaultModels[provider as keyof DefaultModels]

      // 获取项目信息
      let projectInfo: { name: any; description: any; environment: any; template: any; } | undefined = undefined
      if (request.options.projectId) {
        Logger.info('Looking for project with ID:', request.options.projectId)
        Logger.info('Available projects:', projects)
        const project = projects.find((p: any) => p.id === request.options.projectId)
        if (project) {
          Logger.info('Found project:', project)
          projectInfo = {
            name: project.name,
            description: project.description,
            environment: project.environment,
            template: project.template
          }
          Logger.info('Project info created:', projectInfo)
        } else {
          Logger.error('Project not found with ID:', request.options.projectId)
        }
      } else {
        Logger.info('No project ID provided in request')
      }

      // 构建FormRequest对象
      const formRequest: FormRequest = {
        description: request.options.description,
        formFields: request.options.formFields,
        mode: request.options.mode,
        language: request.options.language,
        project: projectInfo,
        provider: provider,
        model: model,
        apiKey: apiKeys[provider],
        useCustomApi: settings.useCustomApi
      }

      // 生成prompt
      const prompt = generatePrompt(formRequest)
      Logger.info('Generated prompt:', prompt)

      // 生成唯一的请求ID
      const requestId = `ai_request_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // 存储真实的prompt和配置
      pendingRequests.set(requestId, {
        prompt,
        provider,
        model,
        apiKey: apiKeys[provider],
        endpoint: provider === 'ollama' ? (settings.ollama_endpoint || apiKeys[provider] || 'http://localhost:11434') : null
      })

      // 使用隐藏的请求方式
      const aiResponse = await makeHiddenAIRequest(requestId)

      // 清理存储的请求数据
      pendingRequests.delete(requestId)

      Logger.info('AI response received:', aiResponse)

      // 清理AI响应
      const cleanedContent = cleanAIResponse(aiResponse.content, provider, model)

      // 构建响应数据
      const responseData = {
        content: cleanedContent,
        usage: aiResponse.usage
      }

      Logger.success('AI request successful:', responseData)

      // 添加详细的数据结构日志
      Logger.info('AI response data structure:', {
        hasData: !!responseData,
        dataType: typeof responseData,
        keys: responseData ? Object.keys(responseData) : [],
        hasContent: responseData && 'content' in responseData,
        hasUsage: responseData && 'usage' in responseData,
        usageStructure: responseData && responseData.usage ? Object.keys(responseData.usage) : [],
        isFormContent: responseData && responseData.content && typeof responseData.content === 'string'
      })

      // 更新token使用统计 (跳过 Ollama，因为它是本地运行的)
      if (settings.useCustomApi && provider !== 'ollama') {
        try {
          // 获取当前token统计
          const tokenStatsStorage = await chrome.storage.sync.get(STORAGE_KEYS.TOKEN_STATS)
          const tokenStats = tokenStatsStorage[STORAGE_KEYS.TOKEN_STATS] || {}

          // 如果没有该provider的统计，初始化
          if (!tokenStats[provider]) {
            tokenStats[provider] = {
              promptTokens: 0,
              completionTokens: 0,
              totalTokens: 0,
              lastUpdated: new Date().toISOString()
            }
          }

          // 确保统计对象具有正确的结构
          if (!tokenStats[provider].promptTokens) tokenStats[provider].promptTokens = 0
          if (!tokenStats[provider].completionTokens) tokenStats[provider].completionTokens = 0
          if (!tokenStats[provider].totalTokens) tokenStats[provider].totalTokens = 0

          // 使用AI响应中的usage数据
          const usage = aiResponse.usage
          const promptTokens = usage.prompt_tokens || 0
          const completionTokens = usage.completion_tokens || 0
          const totalTokens = usage.total_tokens || (promptTokens + completionTokens)

          // 更新统计数据
          tokenStats[provider].promptTokens = (tokenStats[provider].promptTokens || 0) + promptTokens
          tokenStats[provider].completionTokens = (tokenStats[provider].completionTokens || 0) + completionTokens
          tokenStats[provider].totalTokens = (tokenStats[provider].totalTokens || 0) + totalTokens
          tokenStats[provider].lastUpdated = new Date().toISOString()

          Logger.info('Updated token stats with actual usage data:', {
            provider,
            promptTokens,
            completionTokens,
            totalTokens,
            accumulatedTotal: tokenStats[provider].totalTokens
          })

          // 保存更新后的统计
          await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: tokenStats })
          Logger.info('Token usage statistics updated for', provider, tokenStats[provider])
        } catch (statsError) {
          Logger.error('Error updating token statistics:', statsError)
          // 不影响主流程，继续返回AI响应
        }
      }

      return {
        success: true,
        data: responseData
      }

    } catch (error: any) {
      Logger.error('Error in AI request:', error)

      // 处理AI Provider错误
      if (error instanceof AIProviderError) {
        return {
          success: false,
          error: `API error: ${error.message}`
        }
      }

      return {
        success: false,
        error: error.message || 'Failed to process AI request'
      }
    }
  }

  // 验证 API Key
  async function validateApiKey(provider: string, key: string) {
    try {
      let endpoint = '';
      let testPayload = {};

      switch (provider) {
        case 'openai':
          endpoint = 'https://api.openai.com/v1/chat/completions';
          testPayload = {
            model: "gpt-3.5-turbo",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'claude':
          endpoint = 'https://api.anthropic.com/v1/messages';
          testPayload = {
            model: "claude-3-sonnet-20240229",
            max_tokens: 1,
            messages: [{ role: "user", content: "test" }]
          };
          break;

        case 'moonshot':
          endpoint = 'https://api.moonshot.cn/v1/chat/completions';
          testPayload = {
            model: "moonshot-v1-8k",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'gemini':
          endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
          testPayload = {
            contents: [{ parts: [{ text: "test" }] }]
          };
          break;

        case 'deepseek':
          endpoint = 'https://api.deepseek.com/v1/chat/completions';
          testPayload = {
            model: "deepseek-chat",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'openrouter':
          endpoint = 'https://openrouter.ai/api/v1/chat/completions';
          testPayload = {
            model: "openai/gpt-3.5-turbo",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'ollama':
          // 对于 Ollama，我们测试连接到本地服务器
          const ollamaEndpoint = key || 'http://localhost:11434'; // key 作为端点地址
          endpoint = `${ollamaEndpoint}/v1/models`;
          // 对于 Ollama，我们只需要测试模型列表端点
          const modelsResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Authorization': 'Bearer ollama',
              'Accept': 'application/json'
            }
          });

          if (!modelsResponse.ok) {
            if (modelsResponse.status === 0) {
              throw new Error(`Cannot connect to Ollama server at ${ollamaEndpoint}. Please check if Ollama is running and accessible.`);
            }
            throw new Error(`Ollama server responded with status ${modelsResponse.status}. Please check your Ollama installation.`);
          }

          return { success: true };

        default:
          throw new Error('Unsupported provider');
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 根据不同提供商设置认证头
      switch (provider) {
        case 'openai':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'claude':
          headers['x-api-key'] = key;
          headers['anthropic-version'] = '2023-06-01';
          break;
        case 'moonshot':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'gemini':
          endpoint = `${endpoint}?key=${key}`;
          break;
        case 'deepseek':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'openrouter':
          headers['Authorization'] = `Bearer ${key}`;
          headers['HTTP-Referer'] = 'https://fillify.tech';
          headers['X-Title'] = 'Fillify';
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(testPayload)
      });

      const data = await response.json();

      // 检查响应是否包含错误
      if (response.status !== 200) {
        throw new Error(data.error?.message || 'Invalid API key');
      }

      return { success: true };
    } catch (error) {
      Logger.error('API key validation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate API key'
      };
    }
  }

  // Event Listeners
  chrome.runtime.onInstalled.addListener(async (details) => {
    if (details.reason === 'install') {
      Logger.info('Extension installed')
      chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
        if (!data[STORAGE_KEYS.SETTINGS]) {
          const defaultProvider = 'openai'
          const defaultModels = {
            openai: 'gpt-3.5-turbo',
            claude: 'claude-3-sonnet-20240229',
            moonshot: 'moonshot-v1-32k',
            gemini: 'gemini-2.0-flash',
            ollama: 'llama2'
          }

          chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: {
              useCustomApi: true,
              defaultProvider: defaultProvider,
              defaultModel: defaultModels[defaultProvider]
            }
          })
        }
      })

      await checkAndUpdateLoginStatus()
      chrome.tabs.create({
        url: chrome.runtime.getURL('onboarding.html')
      })
    } else if (details.reason === 'update') {
      Logger.info('Extension updated')
      chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
        if (!data[STORAGE_KEYS.SETTINGS]) {
          const defaultProvider = 'openai'
          const defaultModels = {
            openai: 'gpt-3.5-turbo',
            claude: 'claude-3-sonnet-20240229',
            moonshot: 'moonshot-v1-32k',
            gemini: 'gemini-2.0-flash',
            ollama: 'llama2'
          }

          chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: {
              useCustomApi: true,
              defaultProvider: defaultProvider,
              defaultModel: defaultModels[defaultProvider]
            }
          })
        }
      })
    }
  })

  // 添加 cookie 监听功能
  chrome.cookies.onChanged.addListener(async (changeInfo: CookieChangeInfo) => {
    const { cookie, removed, cause } = changeInfo
    Logger.info('Cookie changed:', { cookie, removed, cause })

    if (cookie.domain.includes(COOKIE_DOMAIN) && cookie.name === 'xToken') {
      Logger.info('xToken cookie changed:', { value: cookie.value, removed })
      // 当 cookie 变化时，立即检查登录状态并强制更新用户信息
      const status = await checkLoginStatus()
      if (status && !removed) {
        await fetchUserInfo(cookie.value, true)
      }
    }
  })

  // 注册消息监听器
  chrome.runtime.onMessage.addListener((
    request: Message,
    _sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ) => {
    Logger.info('Received message:', request)

    if (request.type === 'getLoginStatus') {
      // 确保我们返回最新的登录状态
      checkLoginStatus().then(status => {
        sendResponse({ 
          isLoggedIn: status, 
          skipLogin,
          timestamp: Date.now() // 添加时间戳以确保数据新鲜度
        })
      }).catch(error => {
        Logger.error('Error checking login status for getLoginStatus:', error)
        sendResponse({ 
          isLoggedIn: false, 
          skipLogin,
          error: error.message,
          timestamp: Date.now()
        })
      })
      return true
    }

    if (request.type === 'setSkipLogin') {
      setSkipLogin(request.skip).then(() => {
        sendResponse({ success: true })
      }).catch(error => {
        Logger.error('Error setting skip login:', error)
        sendResponse({ success: false, error: String(error) })
      })
      return true
    }

    if (request.type === 'getUserInfo') {
      chrome.cookies.get({
        url: COOKIE_URL,
        name: 'xToken'
      }).then(async (cookie) => {
        if (cookie?.value) {
          const userInfo = await fetchUserInfo(cookie.value)
          sendResponse({ success: true, user: userInfo })
        } else {
          sendResponse({ success: false })
        }
      })
      return true
    }

    if (request.type === 'aiRequest') {
      handleAiRequest(request).then(response => {
        sendResponse(response)
      })
      return true
    }

    if (request.action === 'getSettings') {
      chrome.storage.sync.get([STORAGE_KEYS.SETTINGS, STORAGE_KEYS.API_KEYS], (data) => {
        sendResponse({
          settings: data[STORAGE_KEYS.SETTINGS] || {},
          apiKeys: data[STORAGE_KEYS.API_KEYS] || {}
        })
      })
      return true
    }

    if (request.action === 'updateSettings') {
      chrome.storage.sync.set({
        formify_settings: request.settings
      }, () => {
        sendResponse({ success: true })
      })
      return true
    }
  })
})
