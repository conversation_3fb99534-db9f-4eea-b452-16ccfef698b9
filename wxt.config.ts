import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: 'chrome',
  modules: ['@wxt-dev/module-vue'],
  manifest: {
    name: 'Fillify - AI Form Filler with Ollama,DeepSeek, OpenAI, Claude, Gemini, and More',
    version: '1.0.7',
    description: 'AI-Powered Forms, Emails & Bug Reports Assistant',
    permissions: [
      'storage',
      'activeTab',
      'cookies',
      'declarativeNetRequest'
    ],
    host_permissions: [
      'https://api.openai.com/*',
      'https://api.anthropic.com/*',
      'https://api.moonshot.cn/*',
      'https://api.deepseek.com/*',
      'https://generativelanguage.googleapis.com/*',
      'https://openrouter.ai/*',
      'https://fillify.tech/*',
      'http://localhost:11434/*'  // 添加 Ollama 的特定端口访问权限
    ],
    declarative_net_request: {
      rule_resources: [{
        id: 'ollama_cors_rules',
        enabled: true,
        path: 'rules.json'
      }]
    },
    icons: {
      16: '/icon/icon16.png',
      32: '/icon/icon32.png',
      48: '/icon/icon48.png',
      128: '/icon/icon128.png'
    },
    action: {
      default_icon: {
        16: '/icon/icon16.png',
        32: '/icon/icon32.png',
        48: '/icon/icon48.png',
        128: '/icon/icon128.png'
      }
    },
    options_ui: {
      page: 'settings.html',
      open_in_tab: true
    }
  },
  entrypointsDir: './entrypoints'
});
